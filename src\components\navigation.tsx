"use client"

import Link from "next/link"
import { useState, useEffect, useRef } from "react"
import { usePathname } from "next/navigation"
import { useUserStore } from "@/stores/userStore"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu"
import {
  Search,
  Plus,
  BarChart3,
  User,
  LogOut,
  ChevronDown,
  MapPin,
  DollarSign,
  Ruler,
  Wifi,
  Shield,
  X,
  Building
} from "lucide-react"
import Image from "next/image"

// Filter types
interface Filter {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  type: 'toggle' | 'dropdown'
  options?: string[]
}

export function Navigation() {
  const pathname = usePathname()
  const { user, isAuthenticated, logout, switchRole } = useUserStore()
  const [showUserDropdown, setShowUserDropdown] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [activeFilters, setActiveFilters] = useState<{[key: string]: string}>({})
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Define available filters
  const filters: Filter[] = [
    { id: 'type', label: 'Loại', icon: Building, type: 'dropdown', options: ['Tìm trọ', 'Tìm bạn cùng trọ'] },
    { id: 'location', label: 'Địa chỉ', icon: MapPin, type: 'dropdown', options: ['Hà Nội', 'TP.HCM', 'Đà Nẵng', 'Cần Thơ'] },
    { id: 'area', label: 'Diện tích', icon: Ruler, type: 'dropdown', options: ['< 20m²', '20-30m²', '30-50m²', '> 50m²'] },
    { id: 'price', label: 'Giá cả', icon: DollarSign, type: 'dropdown', options: ['< 2 triệu', '2-5 triệu', '5-10 triệu', '> 10 triệu'] },
    { id: 'amenities', label: 'Tiện ích', icon: Wifi, type: 'dropdown', options: ['Wifi', 'Điều hòa', 'Máy giặt', 'Bếp'] },
    { id: 'rules', label: 'Quy định', icon: Shield, type: 'dropdown', options: ['Cho phép thú cưng', 'Không hút thuốc', 'Giờ giấc tự do'] },
  ]

  // Handle filter selection
  const handleFilterSelect = (filterId: string, value: string) => {
    setActiveFilters(prev => ({
      ...prev,
      [filterId]: value
    }))
  }

  // Remove filter
  const removeFilter = (filterId: string) => {
    setActiveFilters(prev => {
      const newFilters = { ...prev }
      delete newFilters[filterId]
      return newFilters
    })
  }

  // Check if current page is login or register
  const isAuthPage = pathname === '/login' || pathname === '/register'

  const handleLogout = async () => {
    try {
      await logout()
      setShowUserDropdown(false)
      window.location.href = '/'
    } catch (error) {
      console.error('Logout failed:', error)
      // Still redirect even if logout API fails
      setShowUserDropdown(false)
      window.location.href = '/'
    }
  }

  const handleRoleSwitch = () => {
    if (!user) return
    const newRole = user.role === 'tenant' ? 'landlord' : 'tenant'
    switchRole(newRole)
    setShowUserDropdown(false)
    // Redirect to appropriate dashboard
    window.location.href = newRole === 'tenant' ? '/dashboard/tenant' : '/dashboard/landlord'
  }

  const getDashboardLink = () => {
    if (!user) return "/login"
    return user.role === 'tenant' ? '/dashboard/tenant' : '/dashboard/landlord'
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowUserDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <nav className="border-b bg-white shadow-sm relative z-50">
      {/* First Row: Logo, Search, Login/Signup */}
      <div className={isAuthPage ? "" : "border-b border-gray-200"}>
        <div className="container mx-auto px-4">
          <div className="flex h-16 items-center justify-between">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2">
              <Image src="/logo.png" alt="Trustay" width={140} height={140} />
            </Link>

            {/* Search Bar - Hidden on auth pages */}
            {!isAuthPage && (
              <div className="flex-1 max-w-2xl mx-8">
                <div className="flex space-x-2">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      type="text"
                      placeholder="Bạn muốn tìm trọ ở đâu?"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                    />
                    {/* Active Filters Display */}
                    {Object.keys(activeFilters).length > 0 && (
                      <div className="absolute top-full left-0 right-0 bg-white border border-t-0 border-gray-300 rounded-b-md p-2 flex flex-wrap gap-1">
                        {Object.entries(activeFilters).map(([filterId, value]) => {
                          const filter = filters.find(f => f.id === filterId)
                          return (
                            <Badge key={filterId} variant="secondary" className="flex items-center gap-1">
                              {filter?.label}: {value}
                              <X
                                className="h-3 w-3 cursor-pointer hover:text-red-500"
                                onClick={() => removeFilter(filterId)}
                              />
                            </Badge>
                          )
                        })}
                      </div>
                    )}
                  </div>
                  <div className="">
                    <Button
                      className="bg-green-500 hover:bg-green-600 text-white font-medium rounded-lg transition-colors"
                    >
                      Tìm kiếm
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Right Section - Login/Signup or User Menu */}
            <div className="flex items-center space-x-3">
              {isAuthenticated && user ? (
                <>
                  {/* Role Switch Button */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRoleSwitch}
                    className="text-sm border-green-500 text-green-600 hover:bg-green-50"
                  >
                    {user.role === 'tenant' ? 'Quản cáo trọ' : 'Chế độ thuê trọ'}
                  </Button>

                  <div className="relative" ref={dropdownRef}>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowUserDropdown(!showUserDropdown)}
                    className="flex items-center space-x-2 text-gray-700 hover:text-gray-900"
                  >
                    <User className="h-4 w-4" />
                    <span className="hidden sm:inline-block">{user.firstName} {user.lastName}</span>
                    <ChevronDown className="h-3 w-3" />
                  </Button>

                  {showUserDropdown && (
                    <div className="absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg border z-50">
                      <div className="py-1">
                        <Link
                          href="/dashboard/tenant"
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => setShowUserDropdown(false)}
                        >
                          Quản lý cá nhân
                        </Link>
                        {user?.role === 'landlord' && (
                          <Link
                            href="/dashboard/landlord"
                            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            onClick={() => setShowUserDropdown(false)}
                          >
                            Dashboard trọ
                          </Link>
                        )}
                        <button
                          onClick={handleLogout}
                          className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                          <LogOut className="h-4 w-4 inline mr-2" />
                          Đăng xuất
                        </button>
                      </div>
                    </div>
                  )}
                  </div>
                </>
              ) : (
                <div className="flex items-center space-x-2">
                  <Button variant="ghost" size="sm" asChild>
                    <Link href="/login" className="text-gray-600 hover:text-gray-700">
                      Đăng nhập
                    </Link>
                  </Button>
                  <Button size="sm" className="bg-primary hover:bg-green-700 text-white" asChild>
                    <Link href="/register">Đăng ký</Link>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Second Row: Filter Bar - Hidden on auth pages */}
      {!isAuthPage && (
        <div className="container mx-auto px-4">
          <div className="flex h-12 items-center justify-between">
            {/* Post Button */}
            <NavigationMenu viewport={false}>
              <NavigationMenuList>
                <NavigationMenuItem>
                  <NavigationMenuTrigger className="text-white bg-green-600 hover:bg-green-700 font-medium px-4 py-2 rounded-md">
                    <Plus className="h-4 w-4 mr-2" />
                    Đăng bài
                  </NavigationMenuTrigger>
                  <NavigationMenuContent>
                    <ul className="grid gap-3 p-6 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
                      <li className="row-span-3">
                        <NavigationMenuLink asChild>
                          <Link href="/dashboard/landlord/properties/add" className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-muted/50 to-muted p-6 no-underline outline-none focus:shadow-md">
                            <Plus className="h-6 w-6" />
                            <div className="mb-2 mt-4 text-lg font-medium">
                              Đăng tin cho thuê
                            </div>
                            <p className="text-sm leading-tight text-muted-foreground">
                              Đăng tin cho thuê phòng trọ, nhà trọ của bạn
                            </p>
                          </Link>
                        </NavigationMenuLink>
                      </li>
                      <li>
                        <NavigationMenuLink asChild>
                          <Link href="/dashboard/landlord/roommate/add" className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground">
                            <div className="text-sm font-medium leading-none">Đăng tin tìm người ở ghép</div>
                          </Link>
                        </NavigationMenuLink>
                      </li>
                    </ul>
                  </NavigationMenuContent>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>

            {/* Filter Buttons */}
            <div className="flex items-center space-x-4">
              {filters.map((filter) => {
                const IconComponent = filter.icon
                const isActive = activeFilters[filter.id]

                return (
                  <NavigationMenu key={filter.id} viewport={false}>
                    <NavigationMenuList>
                      <NavigationMenuItem>
                        <NavigationMenuTrigger
                          className={`font-medium px-3 py-2 rounded-md flex items-center space-x-2 ${
                            isActive
                              ? 'text-green-600 bg-green-50 border border-green-200'
                              : 'text-gray-600 hover:text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          <IconComponent className="h-4 w-4" />
                          <span>{filter.label}</span>
                          {isActive && <Badge variant="secondary" className="ml-1 text-xs">{isActive}</Badge>}
                        </NavigationMenuTrigger>
                        <NavigationMenuContent>
                          <ul className="grid gap-1 p-2 w-48">
                            {filter.options?.map((option) => (
                              <li key={option}>
                                <NavigationMenuLink asChild>
                                  <button
                                    onClick={() => handleFilterSelect(filter.id, option)}
                                    className="block w-full text-left select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground"
                                  >
                                    <div className="text-sm font-medium leading-none">{option}</div>
                                  </button>
                                </NavigationMenuLink>
                              </li>
                            ))}
                          </ul>
                        </NavigationMenuContent>
                      </NavigationMenuItem>
                    </NavigationMenuList>
                  </NavigationMenu>
                )
              })}
            </div>

            {/* Dashboard Link for authenticated users */}
            {isAuthenticated && user && (
              <Link
                href={getDashboardLink()}
                className="text-gray-600 hover:text-gray-700 font-medium px-3 py-2 rounded-md hover:bg-gray-50 flex items-center space-x-2"
              >
                <BarChart3 className="h-4 w-4" />
                <span className="hidden sm:inline">{user.role === 'tenant' ? 'Dashboard' : 'Quản lý'}</span>
              </Link>
            )}
          </div>
        </div>
      )}
    </nav>
  )
}
