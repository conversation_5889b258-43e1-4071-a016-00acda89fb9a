"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { useUserStore } from "@/stores/userStore"
import {
  User,
  Home,
  Heart,
  Bell,
  LogOut,
  Edit,
  Key,
  Receipt,
  Send
} from "lucide-react"
import Link from "next/link"

export default function TenantPersonalManagement() {
  const { user, logout } = useUserStore()
  const [activeTab, setActiveTab] = useState("profile")

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center space-x-4">
              <div className="h-16 w-16 bg-green-100 rounded-full flex items-center justify-center">
                <User className="h-8 w-8 text-green-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Quản lý cá nhân</h1>
                <p className="text-gray-600">Xin chào, {user?.firstName} {user?.lastName}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="profile" className="flex items-center space-x-2">
              <User className="h-4 w-4" />
              <span className="hidden sm:inline">Thông tin cá nhân</span>
            </TabsTrigger>
            <TabsTrigger value="accommodation" className="flex items-center space-x-2">
              <Home className="h-4 w-4" />
              <span className="hidden sm:inline">Quản lý lưu trú</span>
            </TabsTrigger>
            <TabsTrigger value="requests" className="flex items-center space-x-2">
              <Send className="h-4 w-4" />
              <span className="hidden sm:inline">Yêu cầu thuê</span>
            </TabsTrigger>
            <TabsTrigger value="saved" className="flex items-center space-x-2">
              <Heart className="h-4 w-4" />
              <span className="hidden sm:inline">Trọ đã lưu</span>
            </TabsTrigger>
            <TabsTrigger value="notifications" className="flex items-center space-x-2">
              <Bell className="h-4 w-4" />
              <span className="hidden sm:inline">Thông báo</span>
            </TabsTrigger>
            <TabsTrigger value="logout" className="flex items-center space-x-2">
              <LogOut className="h-4 w-4" />
              <span className="hidden sm:inline">Đăng xuất</span>
            </TabsTrigger>
          </TabsList>

          {/* Thông tin cá nhân */}
          <TabsContent value="profile" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Thông tin cơ bản */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <User className="h-5 w-5" />
                    <span>Thông tin cơ bản</span>
                  </CardTitle>
                  <CardDescription>
                    Thông tin cá nhân và liên hệ của bạn
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Họ</label>
                      <p className="text-gray-900">{user?.firstName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">Tên</label>
                      <p className="text-gray-900">{user?.lastName}</p>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Email</label>
                    <p className="text-gray-900">{user?.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Số điện thoại</label>
                    <p className="text-gray-900">{user?.phone}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Giới tính</label>
                    <p className="text-gray-900">
                      {user?.gender === 'male' ? 'Nam' : user?.gender === 'female' ? 'Nữ' : 'Khác'}
                    </p>
                  </div>
                  <Button className="w-full">
                    <Edit className="h-4 w-4 mr-2" />
                    Cập nhật thông tin
                  </Button>
                </CardContent>
              </Card>

              {/* Đổi mật khẩu */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Key className="h-5 w-5" />
                    <span>Bảo mật</span>
                  </CardTitle>
                  <CardDescription>
                    Quản lý mật khẩu và bảo mật tài khoản
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Mật khẩu hiện tại</label>
                    <p className="text-gray-500">••••••••</p>
                  </div>
                  <Button variant="outline" className="w-full">
                    <Key className="h-4 w-4 mr-2" />
                    Đổi mật khẩu
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Quản lý lưu trú */}
          <TabsContent value="accommodation" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Trọ của tôi */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Home className="h-5 w-5" />
                    <span>Trọ của tôi</span>
                  </CardTitle>
                  <CardDescription>
                    Thông tin về nơi ở hiện tại
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <div className="mx-auto h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                      <Home className="h-8 w-8 text-blue-600" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Chưa có thông tin lưu trú</h3>
                    <p className="text-gray-600 mb-4">
                      Bạn chưa có thông tin lưu trú nào được liên kết
                    </p>
                    <Button>
                      Liên kết thông tin lưu trú
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Hóa đơn */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Receipt className="h-5 w-5" />
                    <span>Hóa đơn</span>
                  </CardTitle>
                  <CardDescription>
                    Lịch sử thanh toán và hóa đơn
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <div className="mx-auto h-16 w-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                      <Receipt className="h-8 w-8 text-green-600" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Chưa có hóa đơn</h3>
                    <p className="text-gray-600 mb-4">
                      Chưa có hóa đơn nào được tạo
                    </p>
                    <Button variant="outline">
                      Xem lịch sử thanh toán
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Yêu cầu thuê */}
          <TabsContent value="requests" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Send className="h-5 w-5" />
                  <span>Yêu cầu thuê của tôi</span>
                </CardTitle>
                <CardDescription>
                  Quản lý các yêu cầu thuê trọ đã gửi
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <div className="mx-auto h-16 w-16 bg-orange-100 rounded-full flex items-center justify-center mb-4">
                    <Send className="h-8 w-8 text-orange-600" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Quản lý yêu cầu thuê</h3>
                  <p className="text-gray-600 mb-4">
                    Xem và quản lý tất cả yêu cầu thuê trọ của bạn
                  </p>
                  <Link href="/dashboard/tenant/requests">
                    <Button>
                      <Send className="h-4 w-4 mr-2" />
                      Xem yêu cầu thuê
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Trọ đã lưu */}
          <TabsContent value="saved" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Heart className="h-5 w-5" />
                  <span>Trọ đã lưu</span>
                </CardTitle>
                <CardDescription>
                  Danh sách các bài viết trọ bạn đã lưu
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <div className="mx-auto h-16 w-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                    <Heart className="h-8 w-8 text-red-600" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Quản lý trọ đã lưu</h3>
                  <p className="text-gray-600 mb-4">
                    Xem và quản lý tất cả bài viết trọ bạn đã lưu
                  </p>
                  <Link href="/dashboard/tenant/saved">
                    <Button>
                      <Heart className="h-4 w-4 mr-2" />
                      Xem trọ đã lưu
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Thông báo */}
          <TabsContent value="notifications" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Bell className="h-5 w-5" />
                  <span>Thông báo</span>
                </CardTitle>
                <CardDescription>
                  Tất cả thông báo và cập nhật
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <div className="mx-auto h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <Bell className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Quản lý thông báo</h3>
                  <p className="text-gray-600 mb-4">
                    Xem tất cả thông báo và cập nhật từ hệ thống
                  </p>
                  <Link href="/dashboard/tenant/notifications">
                    <Button>
                      <Bell className="h-4 w-4 mr-2" />
                      Xem thông báo
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Đăng xuất */}
          <TabsContent value="logout" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <LogOut className="h-5 w-5" />
                  <span>Đăng xuất</span>
                </CardTitle>
                <CardDescription>
                  Thoát khỏi tài khoản của bạn
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <div className="mx-auto h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                    <LogOut className="h-8 w-8 text-gray-600" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Bạn có chắc muốn đăng xuất?</h3>
                  <p className="text-gray-600 mb-6">
                    Bạn sẽ cần đăng nhập lại để sử dụng các tính năng
                  </p>
                  <div className="flex space-x-4 justify-center">
                    <Button variant="outline" onClick={() => setActiveTab("profile")}>
                      Hủy
                    </Button>
                    <Button variant="destructive" onClick={logout}>
                      <LogOut className="h-4 w-4 mr-2" />
                      Đăng xuất
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
